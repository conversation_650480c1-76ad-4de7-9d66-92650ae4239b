<template>
	<view class="goods-details">
		<navbar title="商品详情" :background="{background: `rgba(256,256,256,${percent})`}" :titleColor="`rgba(0,0,0,${percent})`" :immersive="true"></navbar>
		<!-- #ifdef H5 -->
		<download-nav v-if="showDownload" :top="44"></download-nav>
		<!-- #endif -->
		<loading-view v-if="isFirstLoading"></loading-view>
		<view class="contain" v-if="!isNull">
			<bubble-tips top="180rpx"></bubble-tips>
			<product-swiper :imgUrls="swiperList" :video="goodsDetail.video"></product-swiper>
			<!-- 秒杀 -->
			<view class="seckill row-between" v-if="goodsType == 1">
				<view class="price row">
					<view class="row white info">
						<view style="align-items: baseline;" class="row ml20">
							<view class="mr10">秒杀价</view>
							<price-format :first-size="46" :second-size="32" :subscript-size="32"
								:price="goodsDetail.min_price" :weight="500"></price-format>
							<template v-if="goodsDetail.min_price != goodsDetail.max_price">
								<text style="font-size: 46rpx;">-</text>
								<price-format :first-size="46" :second-size="32" :subscript-size="32"
									:show-subscript="false" :price="goodsDetail.max_price" :weight="500"></price-format>
							</template>
							<view class="ml10">
								<price-format :subscript-size="30" :line-through="true" :first-size="30"
									:second-size="30" :price="goodsDetail.market_price">
								</price-format>
							</view>
						</view>
					</view>
				</view>
				<view class="down column-center">
					<view class="xxs primary mb10">距活动结束仅剩</view>
					<u-count-down :timestamp="countTime" @end="getGoodsDetailFun" color="#fff" bg-color="#FF2C3C"
						separator-color="#FF2C3C" font-size="24" height="36" separator-size="26"></u-count-down>
				</view>
			</view>
			<!-- 拼团 -->
			<view class="group" v-show="goodsType == 2">
				<view class="row info" style="height: 100%">
					<view class="row-between ml20 white" style="flex: 1;">
						<view style="align-items: baseline;" class="row">
							<view class="mr10">拼团价</view>
							<price-format :subscript-size="32" :first-size="46" :second-size="32"
								:price="team.team_min_price" :weight="500"></price-format>
							<text class="xs">起</text>
						</view>
						<view class="mr20 row group-num">
							<view class="group-icon">
								<image src="https://yinshua.zueseo.cn/static/uniapp/images/icon_group.png" class="icon-sm"></image>
							</view>
							<view class="xxs ml10 mr10">{{ team.people_num }}人团</view>
						</view>
					</view>
					<view class="down column-center">
						<view class="xxs primary mb10">距活动结束仅剩</view>
						<u-count-down :timestamp="countTime" color="#fff" bg-color="#FF2C3C" separator-color="#FF2C3C"
							font-size="24" height="36" separator-size="26" @end="getGoodsDetailFun"></u-count-down>
					</view>
				</view>
			</view>
			<view class="goods-info bg-white">
				<view class="info-header row" v-if="goodsType != 1">
					<view class="price row flex1">
						<view class="primary mr10">
							<price-format :first-size="46" :second-size="32" :subscript-size="32"
								:price="goodsDetail.min_price" :weight="500"></price-format>
							<template v-if="goodsDetail.min_price != goodsDetail.max_price">
								<text style="font-size: 46rpx;">-</text>
								<price-format :first-size="46" :second-size="32" :subscript-size="32"
									:show-subscript="false" :price="goodsDetail.max_price" :weight="500"></price-format>
							</template>
						</view>
						<view class="line-through muted md">
							<price-format :price="goodsDetail.market_price"></price-format>
						</view>
					</view>
					<image class="icon-share" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_share.png" @tap="showShareBtn = true"></image>
				</view>
				<view class="row" v-if="!goodsType && (goodsDetail.member_price)">
					<view class="vip-price row">
						<view class="price-name xxs">会员价</view>
						<view style="padding: 0 11rpx">
							<price-format :price="goodsDetail.member_price " :first-size="26" :second-size="26"
								:subscript-size="22" :weight="500" color="#7B3200">
							</price-format>
							<text class="xxs" style="color: #7B3200;">起</text>
						</view>
					</view>
				</view>
				<view class="row">
					<view class="name lg bold">{{ goodsDetail.name }}</view>
					<image class="icon-share" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_share.png" @tap="showShareBtn = true"
						v-if="goodsType == 1"></image>
				</view>
				<view class="row-between xs lighter" style="padding: 0 24rpx 20rpx">
					<!-- <text v-if="goodsDetail.stock !== true">库存: {{ goodsDetail.stock }}件</text> -->
					<text>{{ goodsDetail.sales_sum }} 人已下单</text>
					<text>浏览量: {{ goodsDetail.click_count }}次</text>
				</view>
			</view>
			
			<!-- 拼团用户展示卡片 -->
			<group-users
				:user-list="groupUsers"
				:max-display="9"
				:show="goodsType == 2 || groupUsers.length > 0"
				layout="grid"
				:grid-rows="2"
				:grid-cols="5"
				:expandable="true"
				@viewMore="handleViewMoreUsers"
				@toggleExpand="handleToggleExpand">
			</group-users>

			<!-- 用户信息表单 -->
			<view class="user-form bg-white mt20" v-if="goodsType == 2 || showUserForm">
				<view class="form-header">
					<view class="form-title-section row">
						<view class="form-title-icon row-center">
							<image class="icon-sm" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_contact.png"></image>
						</view>
						<view class="form-title-content">
							<view class="form-title-text md bold">填写名片信息</view>
							<view class="form-subtitle xxs lighter">请填写准确的名片信息，确保名片打印时无误</view>
						</view>
					</view>
				</view>

				<view class="form-content">
					<!-- 动态表单字段 -->
					<view 
						v-for="(param, index) in sortedTemplateParams" 
						:key="param.element_uuid"
						class="form-item"
						:class="{'form-item-address': isTextareaField(param), 'form-item-remark': !param.is_required}"
					>
						<view class="form-label">
							<text v-if="param.is_required" class="required-mark">*</text>
							<text class="label-text">{{ param.param_label }}</text>
							<text v-if="!param.is_required" class="optional-text">（选填）</text>
						</view>
						
						<!-- 提示文本 -->
						<view v-if="param.original_text" class="form-hint xxs muted mb10">
							<text>{{ getPlainText(param.original_text) }}</text>
						</view>
						
						<!-- 根据字段类型渲染不同的输入控件 -->
						<!-- 文本输入框 -->
						<input
							v-if="param.param_type == '1' && param.max_length <= 50"
							class="form-input"
							v-model="dynamicFormData[param.element_uuid]"
							:placeholder="getFieldPlaceholder(param)"
							placeholder-class="input-placeholder"
							:maxlength="param.max_length"
							:type="getInputType(param)"
						/>
						
						<!-- 文本域 -->
						<textarea
							v-if="param.param_type == '1' && param.max_length > 50"
							class="form-textarea"
							v-model="dynamicFormData[param.element_uuid]"
							:placeholder="getFieldPlaceholder(param)"
							placeholder-class="input-placeholder"
							:maxlength="param.max_length"
							auto-height
							:show-confirm-bar="false"
						/>
					</view>
					
					<!-- 如果没有动态表单配置，显示默认表单 -->
					<template v-if="!templateParams.length">
						<!-- 用户姓名 -->
						<view class="form-item">
							<view class="form-label">
								<text class="required-mark">*</text>
								<text class="label-text">收货人</text>
							</view>
							<input
								class="form-input"
								v-model="formData.userName"
								placeholder="请输入收货人姓名"
								placeholder-class="input-placeholder"
								maxlength="20"
							/>
						</view>

						<!-- 联系电话 -->
						<view class="form-item">
							<view class="form-label">
								<text class="required-mark">*</text>
								<text class="label-text">联系电话</text>
							</view>
							<input
								class="form-input"
								v-model="formData.phone"
								placeholder="请输入联系电话"
								placeholder-class="input-placeholder"
								type="number"
								maxlength="11"
							/>
						</view>

						<!-- 收货地址 -->
						<view class="form-item form-item-address">
							<view class="form-label">
								<text class="required-mark">*</text>
								<text class="label-text">收货地址</text>
							</view>
							<textarea
								class="form-textarea"
								v-model="formData.address"
								placeholder="请输入详细的收货地址"
								placeholder-class="input-placeholder"
								maxlength="200"
								auto-height
								:show-confirm-bar="false"
							/>
						</view>

						<!-- 备注信息 -->
						<view class="form-item form-item-remark">
							<view class="form-label">
								<text class="label-text">备注信息</text>
								<text class="optional-text">（选填）</text>
							</view>
							<textarea
								class="form-textarea"
								v-model="formData.remark"
								placeholder="如有特殊要求请在此说明"
								placeholder-class="input-placeholder"
								maxlength="100"
								auto-height
								:show-confirm-bar="false"
							/>
						</view>
					</template>
				</view>
			</view>
			
			<view class="group-play bg-white mt20" v-if="goodsType == 2">
				<view class="title">拼团玩法</view>
				<view class="steps row">
					<view class="row step">
						<view class="number xxs">1</view>
						<view class="sm">开团/参团</view>
					</view>
					<view class="line"></view>
					<view class="row step">
						<view class="number xxs">2</view>
						<view class="sm">团满即成新团</view>
					</view>
					<view class="line"></view>
					<view class="row step">
						<view class="number xxs">3</view>
						<view class="sm">满员发货</view>
					</view>
				</view>
			</view>
			<view class="discount mt20 bg-white" v-if="couponList.length || goodsDetail.order_give_integral">
				<view class="row" style="align-items: flex-start;">
					<view class="text muted">优惠</view>
					<view style="flex: 1">
						<view :class="['row coupons', {mb30: goodsDetail.order_give_integral > 0}]"
							v-if="couponList.length" @tap="showCouponFun">
							<view class="flexnone">
								<u-tag text="领券" size="mini" type="primary" mode="plain" />
							</view>
							<view class="con row ml20" style="flex: 1">
								<view v-for="(item, index) in couponList" :key="index" class="coupons-item  mr20">
									<view v-if="index < 2" class="row xs">
										<view class="line1">
											{{ item.use_condition }}
										</view>
									</view>
								</view>
							</view>
							<image class="icon-sm" src="https://yinshua.zueseo.cn/static/uniapp/images/arrow_right.png"></image>
						</view>
						<view class="row integral" style="align-items: flex-start;"
							v-if="goodsDetail.order_give_integral">
							<view class="flexnone">
								<u-tag text="积分" size="mini" type="primary" mode="plain" />
							</view>
							<view class="ml20">下单最多可获得{{goodsDetail.order_give_integral}}积分</view>
						</view>
					</view>
				</view>
			</view>
			<swiper v-if="teamFound.length" class="mt20 bg-white" autoplay="true" style="height: 240rpx;"
				vertical="true" circular="true" :interval="5000">
				<swiper-item v-for="(sitem, index) in teamFound" :key="index">
					<view class="group-list">
						<view v-for="(item, index2) in sitem" :key="index2" class="group-item bg-white row-between">
							<view class="row" style="max-width: 280rpx;">
								<custom-image :src="item.avatar" width="80rpx" height="80rpx" radius="50%">
								</custom-image>
								<view class="ml20 line1 normal">{{ item.nickname }}</view>
							</view>
							<view class="row ml20" style="flex: none;">
								<view class="column-center">
									<text class="sm normal">
										还差
										<text class="primary">{{ item.need - item.join }}</text>
										人成团
									</text>
									<view class="muted xs">
										剩余
										<u-count-down :timestamp="getTeamCountTime(item.found_end_time)"
											separator-color="#999" color="#999" :separator-size="24" :font-size="24"
											bg-color="transparent" @end="getGoodsDetailFun"></u-count-down>
									</view>
								</view>
								<view class="group-btn br60 white row-center" @tap="showSpecFun(3, item.id)">去参团</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
			<view v-if="!goodsType" class="spec row bg-white mt20" @tap="showSpecFun(0)">
				<view class="text lighter">已选</view>
				<view class="line1 mr20" style="flex: 1;">{{ checkedGoods.spec_value_str || '默认' }}</view>
				<image class="icon-sm" src="https://yinshua.zueseo.cn/static/uniapp/images/arrow_right.png"></image>
			</view>
			<!-- <navigator class="mt20" hover-class="none" url="/bundle/pages/server_explan/server_explan?type=2">
				<view class="row bg-white" style="padding: 24rpx 24rpx;">
					<view class="text lighter flex1">售后保障</view>
					<image class="icon-sm" src="https://yinshua.zueseo.cn/static/uniapp/images/arrow_right.png"></image>
				</view>
			</navigator> -->
			<view class="evaluation bg-white mt20">
				<view class="title row-between">
					<view>
						<text class="balck md mr10">用户评价</text>
						<text class="primary sm">好评率{{ comment.goods_rate || '0%' }}</text>
					</view>
					<view class="row">
						<view 
							v-if="isCommentWriter" 
							class="add-comment-btn primary mr20"
							@tap.stop="showVirtualComment = true"
						>添加评价</view>
						<navigator hover-class="none" :url="'/pages/all_comments/all_comments?id=' + goodsDetail.id" class="row">
							<text class="lighter">查看全部</text>
							<image class="icon-sm" src="https://yinshua.zueseo.cn/static/uniapp/images/arrow_right.png"></image>
						</navigator>
					</view>
				</view>
				<view class="con" v-if="comment.goods_rate">
					<view class="user-info row">
						<image class="avatar mr20" :src="comment.avatar"></image>
						<view class="user-name md mr10">{{ comment.nickname }}</view>
					</view>
					<view class="muted xs mt10">
						<text class="mr20">{{ comment.create_time }}</text>
					</view>
					<view v-if="comment.comment" class="dec mt20">{{ comment.comment }}</view>
				</view>
				<view class="con row-center muted" v-else>暂无评价</view>
			</view>

			<view class="goods-like mt20 bg-white" v-if="goodsLike.length">
				<goods-like :list="goodsLike"></goods-like>
			</view>
			<view class="details mt20 bg-white">
				<view class="title lg">商品详情</view>
				<view class="content">
					<u-parse :html="goodsDetail.content" :lazy-load="true" :show-with-animation="true"></u-parse>
				</view>
			</view>
			<view class="footer row bg-white fixed">
				<navigator class="btn column-center" hover-class="none"
					url="/bundle/pages/contact_offical/contact_offical">
					<image class="icon-md" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_contact.png"></image>
					<text class="xxs lighter">客服</text>
				</navigator>
				<button class="btn column-center" hover-class="none" @tap="collectGoodsFun">
					<image class="icon-md"
						:src="goodsDetail.is_collect == 1 ? 'https://yinshua.zueseo.cn/static/uniapp/images/icon_collection_s.png' : 'https://yinshua.zueseo.cn/static/uniapp/images/icon_collection.png'">
					</image>
					<text class="xxs lighter">收藏</text>
				</button>
				<navigator class="btn cart column-center" hover-class="none" open-type="switchTab"
					url="/pages/shop_cart/shop_cart">
					<image class="icon-md" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_cart.png"></image>
					<text class="xxs lighter">购物车</text>
					<u-badge v-if="cartNum" bgColor="#FF2C3C" :offset="[8, 10]" :count="cartNum"></u-badge>
				</navigator>
				<view v-if="btnText.yellow" class="add-cart br60 white mr10 md ml20" @tap="showSpecFun(1)">
					{{ btnText.yellow }}
				</view>
				<view class="right-buy br60 white mr20 ml10 md" @tap="showSpecFun(2)">{{ btnText.red }}</view>
			</view>
		</view>
		<view v-else>
			<view class="details-null column-center">
				<image class="img-null" src="https://yinshua.zueseo.cn/static/uniapp/images/goods_null.png"></image>
				<view class="xs muted">该商品已下架或不存在，去逛逛别的吧~</view>
			</view>
			<recommend></recommend>
		</view>
		<spec-popup :show="showSpec" :goods="goodsDetail" :is-seckill="goodsType == 1" @close="showSpec = false"
			:show-add="popupType == 1 || popupType == 0" :show-buy="popupType == 2 || popupType == 0"
			:showConfirm="popupType == 3" @buynow="onBuy" @addcart="onAddCart" @change="onChangeGoods"
			:group="Boolean(isGroup)" :red-btn-text="btnText.red" :yellow-btn-text="btnText.yellow"
			@confirm="onConfirm"></spec-popup>
			
		<share-popup v-model="showShareBtn" 
			:share-id="id" 
			pagePath="pages/goods_details/goods_details" 
			:type="1" 
			:config="{
				avatar: userInfo.avatar,
				nickname: userInfo.nickname,
				image: goodsDetail.poster || goodsDetail.image,
				price: goodsDetail.min_price,
				marketPrice: goodsDetail.market_price,
				name: goodsDetail.name
		}">
		</share-popup>
		<!-- 领券 -->
		<u-popup v-model="showCoupon" mode="bottom" border-radius="14">
			<view>
				<view class="row-between" style="padding: 30rpx">
					<view class="title md bold">领券</view>
					<view class="close" @tap="showCoupon = false">
						<image class="icon-lg" src="https://yinshua.zueseo.cn/static/uniapp/images/icon_close.png"></image>
					</view>
				</view>
				<view class="content bg-body">
					<scroll-view scroll-y="true" style="height: 700rpx">
						<coupon-list :list="couponList" @reflash="getGoodsCouponFun" :btn-type="3"></coupon-list>
					</scroll-view>
				</view>
			</view>
		</u-popup>

		<view class="share-money" :class="{ show: showCommission && enableCommission}">
			<view class="row-end">
				<view class="share-close row-center" @tap="showCommission=false">
					<u-icon name="close" size="16" color="#fff"></u-icon>
				</view>
			</view>
			<view class="share-con mt10" @tap="showShareBtn=true">
				<view class="primary" style="font-size: 45rpx;">
					{{distribution.earnings}}<text class="xs">元</text>
				</view>
				<view class="lighter xxs">
					好友下单最高可赚
				</view>
			</view>
		</view>
		
		<u-back-top :scroll-top="scrollTop" :top="1000" :customStyle="{ backgroundColor: '#FFF', color: '#000', boxShadow: '0px 3px 6px rgba(0, 0, 0, 0.1)'}"></u-back-top>
		
		<!-- 虚拟评价弹窗 -->
		<virtual-comment v-model="showVirtualComment" :goods-id="id" @success="getGoodsDetailFun"></virtual-comment>
	</view>
</template>

<script>
	import {
		getGoodsDetail,
		addCart,
		getPoster,
		getCartNum
	} from '@/api/store';
	import {
		collectGoods
	} from '@/api/user';
	import {
		getGoodsCoupon,
		teamCheck
	} from '@/api/activity';
	import {
		mapActions,
		mapGetters
	} from 'vuex';
	import {
		arraySlice,
		trottle
	} from '@/utils/tools';
	import {
		toLogin
	} from '@/utils/login';
	import {
		getUser,
		inputInviteCode
	} from '@/api/user';
	import Cache from '@/utils/cache';
	import {
		strToParams
	} from '@/utils/tools'
import virtualComment from '@/components/virtual-comment/virtual-comment.vue';
	import groupUsers from '@/components/group-users/group-users.vue';
	
	export default {
		components: {
			virtualComment,
			groupUsers
		},
		data() {
			return {
				scrollTop: 0,
				percent: 0,
				isFirstLoading: true,
				isNull: false,
				showSpec: false,
				showCoupon: false,
				showShareBtn: false,
				showCommission: true,
				popupType: '',
				swiperList: [],
				goodsDetail: {},
				goodsLike: [],
				goodsType: 0,
				checkedGoods: {},
				couponList: [],
				comment: {},
				countTime: 0,
				tagStyle: {
					img: 'width:100%;'
				},
				team: {},
				teamFound: [],
				isGroup: 0,
				id: '',
				showDownload: false,
				distribution: {},
				showVirtualComment: false,
				// 拼团用户数据
				groupUsers: [],
				// 是否显示用户表单
				showUserForm: true,
				// 动态表单相关数据
				templateParams: [], // 存储接口返回的表单配置
				dynamicFormData: {}, // 动态表单数据，以element_uuid为键
				formRules: {}, // 动态验证规则
				// 保留原有表单数据作为兼容
				formData: {
					userName: '',
					phone: '',
					address: '',
					remark: ''
				}
			};
		},
		onLoad(options) {
			this.onPageScroll = trottle(this.onPageScroll, 500, this)
			if (options && options.scene) {
				let scene = strToParams(decodeURIComponent(options.scene));
				console.log(scene, decodeURIComponent(options.scene))
				options.id = scene.id;
			}
			// #ifdef H5
			if (options && options.isapp == 1) {
				this.showDownload = true;
			}
			// #endif
			if (!options.id) {
				return this.$toast({
					title: '缺少参数，无法查看商品'
				}, {
					tab: 3
				});
			} else {
				this.id = options.id;
			}
			this.getGoodsCouponFun();
			this.getCartNum();
		},
		onShow() {
			this.getGoodsDetailFun();
		},
		onPageScroll(e) {
			const top = uni.upx2px(100)
			const {
				scrollTop
			} = e
			this.percent = scrollTop / top > 1 ? 1 : scrollTop / top
			this.scrollTop = scrollTop
		},
		computed: {
			// 按sort字段排序的模板参数
			sortedTemplateParams() {
				return [...this.templateParams].sort((a, b) => a.sort - b.sort);
			},
			...mapGetters(['isLogin', 'userInfo', 'cartNum']),
			// 按钮文本
			btnText() {
				const {
					goodsType,
					team
				} = this;
				let yellow = '';
				let red = '';
				if (goodsType == 1) {
					red = '立即抢购';
				} else if (goodsType == 2) {
					yellow = '单独购买';
					red = team.people_num + '人团';
				} else {
					yellow = '加入购物车';
					red = '立即购买';
				}
				return {
					yellow,
					red
				};
			},
			// 是否启用佣金显示
			enableCommission() {
				return this.distribution && this.distribution.earnings > 0;
			},
			// 是否为评价写手
			isCommentWriter() {
				return false;
			},
			getTeamCountTime() {
				return time => time - Date.now() / 1000;
			}
		},
		methods: {
			...mapActions(['getCartNum']),
			
			// 动态表单相关方法
			// 初始化动态表单数据
			initDynamicForm() {
				if (this.templateParams && this.templateParams.length > 0) {
					const formData = {};
					this.templateParams.forEach(param => {
						// 使用默认值初始化
						formData[param.element_uuid] = param.default_value || '';
					});
					this.dynamicFormData = formData;
				}
			},
			
			// 获取字段占位符
			getFieldPlaceholder(param) {
				return `请输入${param.param_label}`;
			},
			
			// 获取输入框类型
			getInputType(param) {
				// 根据参数名判断输入类型
				if (param.param_name === 'phone' || param.param_label.includes('电话') || param.param_label.includes('手机')) {
					return 'number';
				}
				return 'text';
			},
			
			// 判断是否为文本域字段
			isTextareaField(param) {
				return param.max_length > 50;
			},
			
			// 获取纯文本（去除HTML标签）
			getPlainText(htmlText) {
				if (!htmlText) return '';
				return htmlText.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ');
			},
			
			// 验证动态表单
			validateDynamicForm() {
				const errors = [];
				this.templateParams.forEach(param => {
					if (param.is_required) {
						const value = this.dynamicFormData[param.element_uuid];
						if (!value || value.trim() === '') {
							errors.push(`${param.param_label}不能为空`);
						}
					}
				});
				return errors;
			},
			
			// 获取表单数据（用于提交）
			getFormSubmitData() {
				if (this.templateParams.length > 0) {
					// 返回动态表单数据
					return this.dynamicFormData;
				} else {
					// 返回静态表单数据
					return this.formData;
				}
			},
			
			// 拼团用户相关方法
			handleViewMoreUsers() {
				// 查看更多拼团用户
				console.log('查看更多拼团用户');
			},
			
			handleToggleExpand(expanded) {
				// 切换展开/收起状态
				console.log('切换展开状态:', expanded);
			},
			async getGoodsDetailFun() {
				const {
					data,
					code
				} = await getGoodsDetail({
					id: this.id
				});
				if (code == 1) {
					let {
						goods_image,
						content,
						comment,
						like,
						activity,
						distribution,
						template_info
					} = data;
					let {
						info,
						team,
						team_found
					} = activity; //秒杀时间
					let time = info ?
						info.end_time - Date.now() / 1000 //拼团时间
						:
						team ?
						team.end_time - Date.now() / 1000 :
						0;

					if (team_found) {
						team_found = arraySlice(team_found, [], 2);
					}
					this.distribution = distribution || {}
					this.goodsDetail = data;
					this.swiperList = goods_image;
					this.comment = comment;
					this.goodsLike = like;
					this.countTime = time;
					this.goodsType = activity.type || 0;
					this.team = team ? team : {};
					this.teamFound = team_found ? team_found : [];
					
					// 如果是拼团商品，添加模拟拼团用户数据
					if (this.goodsType == 2) {
						this.groupUsers = [
							{
								id: 1,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '张小明',
								join_time: Math.floor(Date.now() / 1000) - 3600,
								status: 1
							},
							{
								id: 2,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '李小红',
								join_time: Math.floor(Date.now() / 1000) - 7200,
								status: 0
							},
							{
								id: 3,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '王大力',
								join_time: Math.floor(Date.now() / 1000) - 10800,
								status: 0
							},
							{
								id: 4,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '赵美丽',
								join_time: Math.floor(Date.now() / 1000) - 14400,
								status: 1
							},
							{
								id: 5,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '陈小华',
								join_time: Math.floor(Date.now() / 1000) - 18000,
								status: 0
							},
							{
								id: 6,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '刘志强',
								join_time: Math.floor(Date.now() / 1000) - 21600,
								status: 0
							},
							{
								id: 7,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '杨小芳',
								join_time: Math.floor(Date.now() / 1000) - 25200,
								status: 1
							},
							{
								id: 8,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '周建国',
								join_time: Math.floor(Date.now() / 1000) - 28800,
								status: 0
							},
							{
								id: 9,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '吴小燕',
								join_time: Math.floor(Date.now() / 1000) - 32400,
								status: 0
							},
							{
								id: 10,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '郑大海',
								join_time: Math.floor(Date.now() / 1000) - 36000,
								status: 1
							},
							{
								id: 11,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '孙小玲',
								join_time: Math.floor(Date.now() / 1000) - 39600,
								status: 0
							},
							{
								id: 12,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '马志伟',
								join_time: Math.floor(Date.now() / 1000) - 43200,
								status: 0
							},
							{
								id: 13,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '朱小梅',
								join_time: Math.floor(Date.now() / 1000) - 46800,
								status: 1
							},
							{
								id: 14,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '胡大伟',
								join_time: Math.floor(Date.now() / 1000) - 50400,
								status: 0
							},
							{
								id: 15,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '林小雨',
								join_time: Math.floor(Date.now() / 1000) - 54000,
								status: 0
							},
							{
								id: 16,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '何建华',
								join_time: Math.floor(Date.now() / 1000) - 57600,
								status: 1
							},
							{
								id: 17,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '罗小丽',
								join_time: Math.floor(Date.now() / 1000) - 61200,
								status: 0
							},
							{
								id: 18,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '高志明',
								join_time: Math.floor(Date.now() / 1000) - 64800,
								status: 0
							},
							{
								id: 19,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '梁小花',
								join_time: Math.floor(Date.now() / 1000) - 68400,
								status: 1
							},
							{
								id: 20,
								avatar: 'https://yinshua.zueseo.cn/static/uniapp/images/default_avatar.png',
								nickname: '宋大军',
								join_time: Math.floor(Date.now() / 1000) - 72000,
								status: 0
							}
						];
					} else {
						this.groupUsers = [];
					}

					// 处理动态表单配置
					if (template_info && template_info.params && template_info.params.length > 0) {
						this.templateParams = template_info.params;
						this.initDynamicForm();
					}
					
					this.$nextTick(() => {
						this.isFirstLoading = false;
					});
					// #ifdef H5
					let options = {
						shareTitle: data.name,
						shareLink: location.href + '&invite_code=' + this.userInfo.distribution_code,
						shareImage: data.image,
						shareDesc: data.remark
					};
					this.wxShare(options);
					// #endif
				} else {
					this.isNull = true
					this.isFirstLoading = false;
				}
			},
			async getGoodsCouponFun() {
				const {
					data,
					code
				} = await getGoodsCoupon({
					id: this.id
				});
				if (code == 1) {
					this.couponList = data;
				}
			},
			async collectGoodsFun() {
				if (!this.isLogin) return toLogin();
				const {
					goodsDetail: {
						is_collect
					}
				} = this;
				const {
					data,
					code
				} = await collectGoods({
					is_collect: is_collect == 0 ? 1 : 0,
					goods_id: this.id
				});
				if (code == 1) {
					if (is_collect == 0) {
						this.$toast({
							title: '收藏成功'
						});
					} else {
						this.$toast({
							title: '取消收藏'
						});
					}
					this.getGoodsDetailFun();
				}
			},
			showCouponFun() {
				if (!this.isLogin) return toLogin();
				this.showCoupon = true;
			},
			onChangeGoods(e) {
				console.log(e);
				this.checkedGoods = e.detail;
			},
			showSpecFun(type, id) {
				if (!this.isLogin) return toLogin();
				if (this.goodsType == 2 && [2, 3].includes(type)) {
					this.isGroup = 1;
					this.foundId = id;
				} else {
					this.isGroup = 0;
					this.foundId = '';
				}
				this.popupType = type;
				this.showSpec = true;
			},
			onBuy(e) {
				let {
					id,
					goodsNum
				} = e.detail;
				const {
					goodsType,
					team
				} = this;
				let goods = [{
					item_id: id,
					num: goodsNum
				}];
				const params = {
					goods,
				};
				this.showSpec = false;
				goodsType == 2 ? (params.teamId = team.team_id) : '';
				this.foundId ? (params.foundId = this.foundId) : '';
				uni.navigateTo({
					url: '/pages/confirm_order/confirm_order?data=' + encodeURIComponent((JSON.stringify(params)))
				})
				console.log(1111)
			},
			onConfirm(e) {
				const {
					team: {
						team_id
					}
				} = this;
				teamCheck({
					team_id,
					found_id: this.foundId
				}).then(res => {
					if (res.code == 1) {
						this.onBuy(e);
					}
				});
			},
			async onAddCart(e) {
				let {
					id,
					goodsNum
				} = e.detail;

				if (this.goodsType == 2) {
					// 拼团单独购买
					let goods = [{
						item_id: id,
						num: goodsNum
					}];
					uni.navigateTo({
						url: '/pages/confirm_order/confirm_order?data=' + encodeURIComponent((JSON.stringify({
							goods
						})))
					})
					return
				}
				const {
					code,
					data,
					msg
				} = await addCart({
					item_id: id,
					goods_num: goodsNum
				});
				if (code == 1) {
					this.getCartNum();
					this.$toast({
						title: msg,
						icon: 'success'
					});
					this.showSpec = false;
				}
			},
			// 处理查看更多拼团用户
			handleViewMoreUsers() {
				uni.showToast({
					title: '查看更多用户',
					icon: 'none'
				});
				// 这里可以跳转到拼团用户列表页面
				// uni.navigateTo({
					// url: '/pages/group_users/group_users?goodsId=' + this.id
				// });
			},
			
			// 处理网格布局展开/收起
			handleToggleExpand(data) {
				console.log('展开状态:', data.expanded);
				console.log('用户总数:', data.totalUsers);
			},

			// 表单验证
			validateForm() {
				const { userName, phone, address } = this.formData;

				if (!userName.trim()) {
					uni.showToast({
						title: '请输入收货人姓名',
						icon: 'none'
					});
					return false;
				}

				if (!phone.trim()) {
					uni.showToast({
						title: '请输入联系电话',
						icon: 'none'
					});
					return false;
				}

				// 简单的手机号验证
				const phoneReg = /^1[3-9]\d{9}$/;
				if (!phoneReg.test(phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return false;
				}

				if (!address.trim()) {
					uni.showToast({
						title: '请输入收货地址',
						icon: 'none'
					});
					return false;
				}

				return true;
			},

			// 提交表单（预留接口）
			submitForm() {
				if (!this.validateForm()) {
					return;
				}

				// 这里可以调用API提交表单数据
				console.log('提交表单数据:', this.formData);

				uni.showToast({
					title: '信息已保存',
					icon: 'success'
				});
			},

			// 初始化表单数据（使用模拟数据）
			initFormData() {
				// 模拟数据，实际使用时可以从API获取
				this.formData = {
					userName: '张三',
					phone: '13800138000',
					address: '北京市朝阳区某某街道某某小区某某号楼某某单元某某室',
					remark: '请在工作日送货，谢谢！'
				};
			}
		},
		async onShareAppMessage() {
			const {
				goodsDetail,
				team,
				userInfo
			} = this;
			return {
				title: team.share_title || goodsDetail.name,
				imageUrl: goodsDetail.image,
				path: '/pages/goods_details/goods_details?id=' + this.id + "&invite_code=" + userInfo.distribution_code
			};
		}
	};
</script>

<style lang="scss" scoped>
	/* 优化的动态表单样式 */
	.form-hint {
		color: #666;
		line-height: 1.5;
		font-size: 24rpx;
		margin-bottom: 16rpx;
		padding: 12rpx 16rpx;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		border-radius: 8rpx;
		border-left: 4rpx solid #007bff;
		
		&.original-text {
			background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
			border-left-color: #ffc107;
			color: #856404;
		}
	}

	.form-item {
		margin-bottom: 32rpx;
		position: relative;
		animation: slideInUp 0.6s ease forwards;
		opacity: 0;
		transform: translateY(20rpx);

		&:last-child {
			margin-bottom: 0;
		}

		// 为每个表单项添加延迟动画
		@for $i from 1 through 10 {
			&:nth-child(#{$i}) {
				animation-delay: #{$i * 0.1}s;
			}
		}
	}

	@keyframes slideInUp {
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.form-label {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 16rpx;
		font-weight: 500;
		line-height: 1.4;

		.required-mark {
			color: #ff4757;
			font-size: 32rpx;
			margin-right: 6rpx;
			font-weight: bold;
			text-shadow: 0 1rpx 2rpx rgba(255, 71, 87, 0.3);
		}

		.label-text {
			font-size: 28rpx;
			color: #333;
			flex: 1;
		}

		.optional-text {
			color: #999;
			font-size: 22rpx;
			margin-left: 8rpx;
			padding: 2rpx 8rpx;
			background: #f8f9fa;
			border-radius: 12rpx;
		}
	}

	.form-input, .form-textarea {
		width: 100%;
		padding: 20rpx 16rpx;
		border: 2rpx solid #e8e8e8;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #333;
		background: #fafafa;
		transition: all 0.3s ease;
		box-sizing: border-box;
		
		&:focus {
			border-color: #007bff;
			background: #fff;
			box-shadow: 0 0 0 4rpx rgba(0, 123, 255, 0.1);
			outline: none;
			transform: translateY(-1rpx);
		}
		
		&::placeholder {
			color: #999;
			font-size: 26rpx;
		}
		
		&.error {
			border-color: #ff4757;
			background: #fff5f5;
			animation: shake 0.5s ease-in-out;
		}
	}

	@keyframes shake {
		0%, 100% { transform: translateX(0); }
		25% { transform: translateX(-5rpx); }
		75% { transform: translateX(5rpx); }
	}

	.form-textarea {
		min-height: 120rpx;
		resize: vertical;
		line-height: 1.6;
		font-family: inherit;
	}

	.input-placeholder {
		color: #bbb;
	}

	/* 错误提示样式 */
	.error-message {
		margin-top: 8rpx;
		font-size: 24rpx;
		color: #ff4757;
		display: flex;
		align-items: center;
		animation: fadeIn 0.3s ease;
		
		&::before {
			content: '⚠';
			margin-right: 6rpx;
			font-size: 26rpx;
		}
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(-10rpx); }
		to { opacity: 1; transform: translateY(0); }
	}

	.goods-details {
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom));

		.seckill {
			height: 100rpx;
			background: #ffd4d8;

			.price {
				width: 504rpx;
				height: 100%;
				background: url(https://yinshua.zueseo.cn/static/uniapp/images/bg_seckill.png) no-repeat;
				background-size: 100%;
			}

			.down {
				flex: 1;
			}
		}

		.group {
			height: 100rpx;
			width: 100%;
			background-image: url(https://yinshua.zueseo.cn/static/uniapp/images/pintuan_bg.png);
			background-size: 100%;

			.group-num {
				border: 1px solid #ffffff;
				border-radius: 4rpx;

				.group-icon {
					background: #fff;
					padding: 3rpx 7rpx;
				}
			}

			.down {
				height: 100%;
				background-color: #fff5e1;
				padding: 0 20rpx;
			}
		}

		.goods-info {
			position: relative;

			.info-header {
				padding: 20rpx 0 0rpx 24rpx;

				.price {
					align-items: baseline;
				}
			}

			.vip-price {
				margin: 0 24rpx;
				background-color: #FFE9BA;
				color: #FFD4B7;
				line-height: 36rpx;
				border-radius: 6rpx;
				overflow: hidden;

				.price-name {
					background-color: #101010;
					padding: 3rpx 12rpx;
					position: relative;
					overflow: hidden;

					&::after {
						content: '';
						display: block;
						width: 20rpx;
						height: 20rpx;
						position: absolute;
						right: -15rpx;
						background-color: #FFE9BA;
						border-radius: 50%;
						top: 50%;
						transform: translateY(-50%);
						box-sizing: border-box;
					}
				}
			}

			.name {
				padding: 20rpx 24rpx;
				flex: 1;
			}

			.icon-share {
				width: 134rpx;
				height: 60rpx;
			}
		}

		.discount {
			padding: 24rpx;

			.text {
				width: 100rpx;
				flex: none;
			}

			.con {
				width: 400rpx;
			}

			.coupons-item {
				overflow: hidden;

				&>view {
					position: relative;
					height: 40rpx;
					line-height: 40rpx;
					padding: 0 18rpx;
					border-radius: 6rpx;
					box-sizing: border-box;
					background-color: $-color-primary;
					color: #fff;
					white-space: nowrap;
					overflow: hidden;

					&::after,
					&::before {
						content: '';
						display: block;
						width: 20rpx;
						height: 20rpx;
						position: absolute;
						left: -14rpx;
						background-color: #fff;
						border-radius: 50%;
						border: 1px solid currentColor;
						box-sizing: border-box;
					}

					&::after {
						right: -14rpx;
						left: auto;
					}
				}
			}
		}

		.details-null {
			padding-top: 140rpx;
			margin-bottom: 100rpx;
		}

		.spec {
			padding: 24rpx;

			.text {
				width: 100rpx;
			}
		}

		.evaluation {
			.title {
				height: 100rpx;
				border-bottom: $-solid-border;
				padding: 0 24rpx;
				
				.add-comment-btn {
					font-size: 26rpx;
					padding: 6rpx 16rpx;
					border: 1px solid currentColor;
					border-radius: 30rpx;
				}
			}

			.con {
				padding: 30rpx 24rpx;
			}

			.user-info .avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
			}
		}

		.details {
			.title {
				line-height: 88rpx;
				text-align: center;
			}

			&>.content {
				padding: 0 20rpx 20rpx;
				overflow: hidden;

				::v-deep image {
					vertical-align: middle;
				}

				// #ifdef H5
				::v-deep img {
					vertical-align: middle;
				}

				// #endif
				// #ifdef MP-WEIXIN || APP-PLUS
				::v-deep ._img {
					display: block;
				}

				// #endif
			}
		}

		.footer {
			height: 100rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			box-sizing: content-box;
			padding-bottom: env(safe-area-inset-bottom);

			.btn {
				width: 100rpx;
				height: 100rpx;
				position: relative;
				line-height: 1.3;
			}

			.cart-num {
				position: absolute;
				left: 60rpx;
				top: 6rpx;
			}

			.add-cart,
			.right-buy {
				flex: 1;
				text-align: center;
				padding: 16rpx 0;
			}

			.add-cart {
				background-color: #ffa630;
			}

			.right-buy {
				background-color: $-color-primary;
			}
		}

		.group-play {
			.title {
				padding: 20rpx 28rpx;
				border-bottom: $-solid-border;
			}

			.steps {
				padding: 20rpx 28rpx;

				.step {
					flex: none;
				}

				.line {
					flex: 1;
					border: 1px dashed #999999;
					margin: 0 20rpx;
				}

				.number {
					border: 1rpx solid #707070;
					width: 28rpx;
					height: 28rpx;
					border-radius: 50%;
					line-height: 28rpx;
					text-align: center;
					margin-right: 6rpx;
				}
			}
		}

		.group-list {
			.group-item {
				padding: 20rpx 24rpx;

				&:not(:last-of-type) {
					border-bottom: $-solid-border;
				}

				.group-btn {
					background: linear-gradient(90deg, #f95f2f 0%, #ff2c3c 100%);
					height: 58rpx;
					padding-left: 28rpx;
					padding-right: 28rpx;
					margin-left: 30rpx;
					box-shadow: 0px 6rpx 12rpx rgba(249, 47, 138, 0.4);
				}
			}
		}

		.share-money {
			position: fixed;
			left: 20rpx;
			bottom: calc(130rpx + env(safe-area-inset-bottom));
			transform: scale(0);
			transition: all .3s;

			&.show {
				transform: scale(1);
			}

			.share-close {
				width: 34rpx;
				height: 34rpx;
				background: #a7a7a7;
				border-radius: 50%;
			}

			.share-con {
				background: url('https://yinshua.zueseo.cn/static/uniapp/images/bg_packet_img.png');
				width: 241rpx;
				height: 208rpx;
				background-size: 100%;
				padding-top: 20rpx;
				text-align: center;
			}
		}
	}

	// 优化的用户表单样式
	.user-form {
		border-radius: 20rpx;
		overflow: hidden;
		background: #fff;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		position: relative;

		// 添加顶部装饰条
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 4rpx;
			background: linear-gradient(90deg, $-color-primary 0%, #ff6b6b 50%, #ffc107 100%);
		}

		.form-header {
			padding: 28rpx 24rpx 20rpx;
			border-bottom: 1px solid #f0f0f0;
			background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);

			.form-title-section {
				align-items: flex-start;
			}

			.form-title-icon {
				width: 48rpx;
				height: 48rpx;
				background: linear-gradient(135deg, $-color-primary, #ff6b6b);
				border-radius: 50%;
				margin-right: 16rpx;
				flex: none;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);

				.icon-sm {
					filter: brightness(0) invert(1);
				}
			}

			.form-title-content {
				flex: 1;
				min-width: 0;

				.form-title-text {
					color: $-color-normal;
					margin-bottom: 6rpx;
					font-weight: 600;
					font-size: 30rpx;
				}

				.form-subtitle {
					color: $-color-muted;
					line-height: 1.5;
					font-size: 26rpx;
				}
			}
		}

		.form-content {
			padding: 12rpx 0 28rpx;

			.form-item {
				display: flex;
				align-items: center;
				padding: 24rpx;
				border-bottom: 1px solid #f0f0f0;
				position: relative;
				transition: all 0.3s ease;
				min-height: 88rpx;
				box-sizing: border-box;

				&:hover {
					background: rgba(0, 123, 255, 0.02);
				}

				&:last-child {
					border-bottom: none;
				}

				// 多行文本项特殊处理
				&.form-item-address,
				&.form-item-remark {
					align-items: flex-start;

					.form-label {
						padding-top: 16rpx;
						line-height: 1.3;
					}
				}

				.form-label {
					width: 140rpx;
					flex: none;
					display: flex;
					align-items: center;
					margin-right: 20rpx;
					line-height: 44rpx;
					height: 44rpx;

					.required-mark {
						color: #ff4757;
						font-size: 28rpx;
						margin-right: 6rpx;
						font-weight: bold;
						line-height: 44rpx;
					}

					.label-text {
						color: $-color-normal;
						font-size: 28rpx;
						font-weight: 500;
						line-height: 44rpx;
						white-space: nowrap;
					}

					.optional-text {
						color: $-color-muted;
						font-size: 22rpx;
						margin-left: 6rpx;
						padding: 2rpx 8rpx;
						background: #f0f0f0;
						border-radius: 10rpx;
						line-height: 1.2;
					}
				}

				.form-input {
					flex: 1;
					font-size: 28rpx;
					color: $-color-normal;
					padding: 12rpx 16rpx;
					margin: 0;
					background: #fafafa;
					border: 1rpx solid #e8e8e8;
					border-radius: 8rpx;
					transition: all 0.3s ease;
					box-sizing: border-box;
					line-height: 44rpx;
					height: 44rpx;

					// 默认单行显示，超出省略
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					&:focus {
						background: #fff;
						border-color: $-color-primary;
						box-shadow: 0 0 0 2rpx rgba(0, 123, 255, 0.1);
						outline: none;
						// 聚焦时允许多行显示
						white-space: normal;
						word-wrap: break-word;
						height: auto;
						min-height: 44rpx;
					}

					&.input-placeholder {
						color: $-color-muted;
					}

					&::placeholder {
						color: #bbb;
						font-size: 26rpx;
					}

					// 多行显示模式类
					&.multiline {
						white-space: normal;
						word-wrap: break-word;
						height: auto;
						min-height: 66rpx;
						line-height: 1.5;
					}
				}

				.form-textarea {
					flex: 1;
					font-size: 28rpx;
					color: $-color-normal;
					padding: 12rpx 16rpx;
					margin: 0;
					background: #fafafa;
					border: 1rpx solid #e8e8e8;
					border-radius: 8rpx;
					min-height: 88rpx;
					line-height: 1.5;
					transition: all 0.3s ease;
					box-sizing: border-box;
					resize: vertical;
					word-wrap: break-word;

					&:focus {
						background: #fff;
						border-color: $-color-primary;
						box-shadow: 0 0 0 2rpx rgba(0, 123, 255, 0.1);
						outline: none;
					}

					&.input-placeholder {
						color: $-color-muted;
					}

					&::placeholder {
						color: #bbb;
						font-size: 26rpx;
					}

					// 限制最大高度，超出显示滚动条
					&.limited {
						max-height: 200rpx;
						overflow-y: auto;
					}
				}
			}
		}
}

// 响应式适配
@media (max-width: 750rpx) {
	.goods-details .user-form {
		border-radius: 16rpx;

		.form-header {
			padding: 20rpx 20rpx 12rpx;

			.form-title-icon {
				width: 40rpx;
				height: 40rpx;
				margin-right: 12rpx;
			}
		}

		.form-content {
			.form-item {
				padding: 16rpx 20rpx;

				.form-label {
					width: 120rpx;
				}
			}
		}
	}
}
</style>
